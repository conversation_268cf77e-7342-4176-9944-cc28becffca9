# Hireista - Product Requirements Document

## 1. Product Overview

Hireista is a swipe-based mobile marketplace for remote-friendly tech jobs, monetized by tiered in-app subscriptions. The platform connects job seekers with remote tech opportunities through an intuitive Tinder-like interface, offering different subscription tiers with varying daily swipe limits.

The product solves the problem of inefficient job searching by providing a curated, mobile-first experience that makes discovering remote tech jobs engaging and efficient. Target users are tech professionals seeking remote work opportunities who prefer a streamlined, mobile-native job discovery experience.

## 2. Core Features

### 2.1 User Roles

| Role      | Registration Method           | Core Permissions                                         |
| --------- | ----------------------------- | -------------------------------------------------------- |
| Free User | Email/Magic Link via Supabase | 5 daily swipes, basic job browsing, bookmarks            |
| Plus User | Subscription upgrade via IAP  | 50 daily swipes, advanced filters, priority support      |
| Pro User  | Subscription upgrade via IAP  | 150 daily swipes, all features, analytics dashboard      |
| Admin     | Backend access                | Full CRUD operations, telemetry viewing, user management |

### 2.2 Feature Module

Our Hireista requirements consist of the following main pages:

1. **Swipe Feed**: Infinite card stack of active jobs with swipe gestures for like/pass actions
2. **Bookmarks**: Saved jobs collection with filtering and search capabilities
3. **Search & Filters**: Advanced job filtering by keyword, salary range, timezone, equity options
4. **Profile & Subscription**: User profile management and subscription tier upgrades
5. **Job Details**: Detailed job information with instant apply functionality
6. **Admin Dashboard**: Web-only interface for job management and analytics
7. **Onboarding**: User setup and subscription tier selection

### 2.3 Page Details

| Page Name              | Module Name             | Feature Description                                                                         |
| ---------------------- | ----------------------- | ------------------------------------------------------------------------------------------- |
| Swipe Feed             | Job Cards               | Display infinite stack of job cards with company logo, title, salary, location, and tags    |
| Swipe Feed             | Swipe Gestures          | Handle left swipe (pass) and right swipe (like) with visual feedback and animations         |
| Swipe Feed             | Tier Limits             | Enforce daily swipe limits (5/50/150) based on subscription tier, reset at 00:00 local time |
| Swipe Feed             | Job Sources             | Integrate RemoteOK and other job APIs for continuous job feed                               |
| Bookmarks              | Saved Jobs              | Store and display user's bookmarked jobs with sorting options                               |
| Bookmarks              | Job Management          | Allow removal of bookmarks and bulk actions                                                 |
| Search & Filters       | Keyword Search          | Search jobs by title, company, or description keywords                                      |
| Search & Filters       | Salary Filter           | Filter by minimum and maximum salary ranges                                                 |
| Search & Filters       | Location Filter         | Filter by timezone and remote work preferences                                              |
| Search & Filters       | Equity Filter           | Filter jobs offering equity compensation                                                    |
| Profile & Subscription | User Profile            | Display user information, preferences, and swipe statistics                                 |
| Profile & Subscription | Subscription Management | Upgrade/downgrade subscription tiers with Stripe IAP integration                            |
| Profile & Subscription | Usage Analytics         | Show daily swipe count, remaining swipes, and usage patterns                                |
| Job Details            | Job Information         | Display comprehensive job details including description, requirements, and qualifications   |
| Job Details            | Instant Apply           | Deep-link to original job posting for direct application                                    |
| Job Details            | Company Info            | Show company details, logo, and additional context                                          |
| Admin Dashboard        | Job CRUD                | Create, read, update, delete job postings                                                   |
| Admin Dashboard        | Analytics               | View swipe funnels, user retention, and conversion metrics                                  |
| Admin Dashboard        | User Management         | Monitor user activity and subscription status                                               |
| Onboarding             | Welcome Flow            | Guide new users through app features and subscription options                               |
| Onboarding             | Preference Setup        | Collect user job preferences for personalized recommendations                               |

## 3. Core Process

**User Registration and Onboarding Flow:**
New users sign up via email/magic link through Supabase authentication, complete profile setup with job preferences, and select initial subscription tier (Free by default with upgrade prompts).

**Daily Job Discovery Flow:**
Users open the app to the swipe feed, browse through job cards with swipe gestures, bookmark interesting positions, and apply instantly through deep-links. The system tracks daily swipe count and enforces tier limits.

**Subscription Management Flow:**
Users can upgrade their subscription tier through native IAP, manage billing through Stripe, and access tier-specific features like increased swipe limits and advanced filters.

**Admin Management Flow:**
Admins access the web dashboard to manage job postings, monitor user analytics, and oversee platform operations including job ingestion and data quality.

```mermaid
graph TD
    A[App Launch] --> B[Authentication Check]
    B --> C[Onboarding Flow]
    B --> D[Swipe Feed]
    C --> E[Profile Setup]
    E --> F[Subscription Selection]
    F --> D
    D --> G[Job Card Swipe]
    G --> H[Like - Bookmark]
    G --> I[Pass - Next Card]
    H --> J[Job Details]
    I --> K[Check Swipe Limit]
    K --> L[Upgrade Prompt]
    K --> D
    J --> M[Instant Apply]
    D --> N[Search & Filters]
    D --> O[Bookmarks]
    D --> P[Profile & Settings]
    P --> Q[Subscription Management]
```

## 4. User Interface Design

### 4.1 Design Style

* **Primary Colors**: Modern gradient from #6366F1 (indigo) to #8B5CF6 (purple)

* **Secondary Colors**: #F3F4F6 (light gray), #1F2937 (dark gray)

* **Button Style**: Rounded corners (12px radius), subtle shadows, gradient backgrounds

* **Font**: SF Pro Display (iOS) / Roboto (Android), sizes 14-24px for body, 28-32px for headers

* **Layout Style**: Card-based design with generous white space, bottom tab navigation

* **Icons**: Outlined style with 2px stroke weight, consistent sizing at 20-24px

* **Animations**: Smooth spring animations for swipe gestures, fade transitions between screens

### 4.2 Page Design Overview

| Page Name        | Module Name        | UI Elements                                                                               |
| ---------------- | ------------------ | ----------------------------------------------------------------------------------------- |
| Swipe Feed       | Job Cards          | Full-screen cards with rounded corners, company logo, gradient overlays, swipe indicators |
| Swipe Feed       | Action Buttons     | Floating action buttons for pass/like with haptic feedback                                |
| Swipe Feed       | Progress Indicator | Daily swipe counter with circular progress bar                                            |
| Bookmarks        | Job List           | Grid layout with thumbnail images, compact job information                                |
| Search & Filters | Filter Panel       | Sliding bottom sheet with range sliders, toggle switches, search input                    |
| Profile          | Stats Dashboard    | Card-based layout with usage statistics, subscription status                              |
| Profile          | Subscription Tiers | Pricing cards with feature comparison, upgrade CTAs                                       |
| Job Details      | Content Layout     | Scrollable content with sticky header, floating apply button                              |
| Admin Dashboard  | Data Tables        | Responsive tables with sorting, filtering, pagination                                     |
| Admin Dashboard  | Analytics Charts   | Interactive charts for metrics visualization                                              |

### 4.3 Responsiveness

The product is mobile-first with responsive design for tablets. Touch interactions are optimized for gesture-based navigation, with minimum touch targets of 44px. The interface adapts to different screen sizes while maintaining consistent spacing and typography scales.

## 5. Technical Implementation Notes

### 5.1 Subscription Tiers

* **Free Tier**: 5 daily swipes, basic features

* **Plus Tier**: 50 daily swipes, advanced filters, priority support

* **Pro Tier**: 150 daily swipes, all features, analytics access

### 5.2 Daily Limits

* Swipe counters reset at 00:00 local timezone

* Persistent storage of daily usage in Supabase

* Real-time limit enforcement with upgrade prompts

### 5.3 Job Sources

* Primary: RemoteOK API integration

* Secondary: Additional job board APIs (configurable)

* Job ingestion worker runs every 3 hours

* Deduplication based on job title, company, and description similarity

### 5.4 Analytics Integration

* PostHog for user behavior tracking

* Swipe funnel analysis

* Retention and conversion metrics

* A/B testing capabilities for feature optimization

### 5.5 Future Features (Stub Implementation)

* Voice search functionality

* AI-powered resume matching

* Advanced recommendation algorithms

* Social features and job sharing

