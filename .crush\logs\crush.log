{"time":"2025-08-08T17:55:20.8267637-07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":113},"msg":"Getting live provider data"}
{"time":"2025-08-08T17:55:21.3828892-07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-08T17:55:24.5284643-07:00","level":"INFO","msg":"OK   20250424200609_initial.sql (5.55ms)"}
{"time":"2025-08-08T17:55:24.5331076-07:00","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (4.02ms)"}
{"time":"2025-08-08T17:55:24.5342149-07:00","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (1.11ms)"}
{"time":"2025-08-08T17:55:24.5368929-07:00","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (2.68ms)"}
{"time":"2025-08-08T17:55:24.5368929-07:00","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-08-08T17:55:24.5400027-07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-08T17:55:24.5782438-07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-08T17:55:24.5836341-07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-08T17:55:24.637082-07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-08T17:55:24.637082-07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-08T17:55:38.4595753-07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-08T17:55:38.4595753-07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-08T17:55:38.4706537-07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-08T17:55:38.4706537-07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-08T17:55:39.0563904-07:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Run.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":343},"msg":"failed to process events: POST \"https://openrouter.ai/api/v1/chat/completions\": 402 Payment Required {\"message\":\"This request requires more credits, or fewer max_tokens. You requested up to 32000 tokens, but can only afford 2661. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account\",\"code\":402,\"metadata\":{\"provider_name\":null}}"}
