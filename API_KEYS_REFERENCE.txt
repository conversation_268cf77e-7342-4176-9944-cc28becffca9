# API KEYS REFERENCE
# This file contains all API keys and configuration needed for the Hireista/Jobbify application
# Keep this file secure and do not commit to version control

================================================================================
                                JOB API KEYS
================================================================================

1. ASHBY API
   - Purpose: Job listings from Ashby job boards
   - Key Variable: ASHBY_API_KEY
   - Documentation: https://developers.ashbyhq.com/
   - Usage: Used in ashbyJobsService.ts
   - Notes: Requires company-specific job board configuration

2. RAPIDAPI JOBS
   - Purpose: Job listings aggregation
   - Key Variable: RAPIDAPI_KEY
   - Documentation: https://rapidapi.com/
   - Usage: Used in rapidApiJobsService.ts
   - Notes: Multiple job sources available

3. REMOTEOK API
   - Purpose: Remote job listings
   - Key Variable: REMOTEOK_API_KEY (if required)
   - Documentation: https://remoteok.io/api
   - Usage: Used in remoteOkService.ts
   - Notes: Some endpoints may not require API key

4. ADZUNA API
   - Purpose: Job search aggregation
   - Key Variables: ADZUNA_APP_ID, ADZUNA_APP_KEY
   - Documentation: https://developer.adzuna.com/
   - Usage: Used in job-api backend
   - Notes: Provides job listings from multiple sources

5. JOOBLE API
   - Purpose: Job search aggregation from multiple sources
   - Key Variable: JOOBLE_API_KEY
   - Documentation: https://jooble.org/api/about
   - Usage: Used in job-api backend (job_apis.py)
   - Notes: Free tier with 1000 daily requests, supports location-based search

6. THE MUSE API
   - Purpose: Job listings from The Muse platform
   - Key Variable: MUSE_API_KEY
   - Documentation: https://www.themuse.com/developers/api/v2
   - Usage: Used in job-api backend (job_apis.py)
   - Notes: Provides tech and business job listings with company information

7. ARBEITNOW API
   - Purpose: European job listings aggregation
   - Key Variable: No API key required (public API)
   - Documentation: https://arbeitnow.com/api
   - Usage: Used in job-api backend (job_service.py)
   - Notes: Free API for European job market, no authentication required

================================================================================
                                AI API KEYS
================================================================================

1. OPENROUTER API
   - Purpose: AI model access for various LLMs
   - Key Variable: OPENROUTER_API_KEY
   - Documentation: https://openrouter.ai/docs
   - Usage: Used in openRouterService.ts
   - Models: GPT-4, Claude, Llama, etc.
   - Notes: Pay-per-use pricing

2. DEEPSEEK API
   - Purpose: AI assistance and code generation
   - Key Variable: DEEPSEEK_API_KEY
   - Documentation: https://platform.deepseek.com/
   - Usage: Used in deepseekService.ts
   - Notes: Alternative AI provider

3. OPENAI API (if used directly)
   - Purpose: GPT models for AI assistance
   - Key Variable: OPENAI_API_KEY
   - Documentation: https://platform.openai.com/docs
   - Usage: Direct OpenAI integration
   - Notes: May be used through OpenRouter instead

================================================================================
                            DATABASE & BACKEND
================================================================================

1. SUPABASE
   - Purpose: Database, authentication, and backend services
   - Key Variables:
     * SUPABASE_URL
     * SUPABASE_ANON_KEY
     * SUPABASE_SERVICE_ROLE_KEY (server-side only)
   - Documentation: https://supabase.com/docs
   - Usage: Used throughout the app for data storage
   - Notes: Main backend infrastructure

2. GOOGLE SERVICES
   - Purpose: Google OAuth authentication and Firebase services
   - Key Variables:
     * GOOGLE_WEB_CLIENT_ID
     * GOOGLE_IOS_CLIENT_ID
   - File: google-services.json (Android configuration)
   - Documentation: https://firebase.google.com/docs
   - Usage: Cross-platform authentication (authUtils.ts, app.config.js)
   - Notes: Separate client IDs for web and iOS platforms

3. SLACK WEBHOOKS
   - Purpose: Alert notifications and monitoring
   - Key Variable: SLACK_WEBHOOK_URL
   - Documentation: https://api.slack.com/messaging/webhooks
   - Usage: Used in job-api monitoring (alertmanager.yml)
   - Notes: For critical alerts, warnings, and job-api specific notifications

4. OPENAI API (DIRECT)
   - Purpose: AI assistant and chat completions
   - Key Variable: OPENAI_API_KEY
   - Documentation: https://platform.openai.com/docs/api-reference
   - Usage: Used in aiAssistantService.ts for direct OpenAI calls
   - Notes: Alternative to OpenRouter for OpenAI models

5. PROMETHEUS METRICS
   - Purpose: Application monitoring and metrics collection
   - Key Variable: None (built-in endpoint)
   - Documentation: https://prometheus.io/docs/
   - Usage: Available at /metrics endpoint in job-api
   - Notes: Provides performance and health metrics

6. HEALTH CHECK ENDPOINTS
   - Purpose: Service health monitoring and readiness probes
   - Key Variables: None (built-in endpoints)
   - Endpoints: /health, /health/liveness, /health/readiness
   - Usage: Used by Kubernetes and monitoring systems
   - Notes: Essential for production deployment health checks

================================================================================
                            FILE PROCESSING
================================================================================

1. FILE TEXT EXTRACTION API
   - Purpose: Extract text from PDF/document files
   - Key Variable: FILE_EXTRACTION_API_KEY
   - Usage: Used in fileTextExtractService.ts
   - Notes: For resume and document processing

2. PDF.CO API
   - Purpose: PDF text extraction and processing
   - Key Variable: PDFCO_API_KEY
   - Documentation: https://pdf.co/
   - Usage: Used in fileTextExtractService.ts
   - Notes: Alternative PDF processing service

3. GEMINI BACKEND API
   - Purpose: AI-powered resume analysis
   - Backend URL: FILE_TEXT_BACKEND_URL
   - Usage: Used in resume-advisor for AI analysis
   - Notes: Local backend service using OpenRouter/Gemini

================================================================================
                            BACKEND SERVICES
================================================================================

1. REDIS CACHE
   - Purpose: Caching and session management
   - Key Variable: REDIS_URL
   - Documentation: https://redis.io/docs
   - Usage: Used in job-api backend
   - Notes: Optional for development, required for production

2. API ENDPOINTS CONFIGURATION
   - Purpose: Backend API URL configuration for different environments
   - Key Variable: API_URL (auto-detected in remoteOkService.ts)
   - Default URLs:
     * Production: https://api.jobbify.com
     * Development: http://localhost:8000
     * Network IP: http://**********:8000
     * Android Emulator: http://********:8000
   - Usage: Used throughout frontend services
   - Notes: Automatically detects best available endpoint

3. FILE TEXT BACKEND
   - Purpose: PDF and document text extraction service
   - Key Variable: FILE_TEXT_BACKEND_URL
   - Default URL: http://**********:5000
   - Usage: Used in fileTextExtractService.ts
   - Notes: Local backend service for resume processing

================================================================================
                            ENVIRONMENT SETUP
================================================================================

For development, create a .env file in the Jobbify directory with:

# Job APIs
ASHBY_API_KEY=your_ashby_key_here
RAPIDAPI_KEY=your_rapidapi_key_here
REMOTEOK_API_KEY=your_remoteok_key_here
ADZUNA_APP_ID=your_adzuna_app_id
ADZUNA_APP_KEY=your_adzuna_app_key
JOOBLE_API_KEY=your_jooble_api_key
MUSE_API_KEY=your_muse_api_key
# Note: Arbeitnow API requires no authentication

# AI APIs
EXPO_PUBLIC_OPENROUTER_API_KEY=your_openrouter_key_here
DEEPSEEK_API_KEY=your_deepseek_key_here
EXPO_PUBLIC_OPENAI_API_KEY=your_openai_key_here
OPENAI_API_KEY=your_openai_api_key

# Supabase
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_service_key

# Google Services
GOOGLE_WEB_CLIENT_ID=your_google_web_client_id
GOOGLE_IOS_CLIENT_ID=your_google_ios_client_id

# Monitoring & Alerts
SLACK_WEBHOOK_URL=your_slack_webhook_url

# File Processing
FILE_EXTRACTION_API_KEY=your_file_extraction_key
PDFCO_API_KEY=your_pdfco_key

# Backend Services
REDIS_URL=redis://localhost:6379
FILE_TEXT_BACKEND_URL=http://localhost:5000
API_URL=http://localhost:8000

# Site Configuration
EXPO_PUBLIC_SITE_URL=http://localhost
EXPO_PUBLIC_SITE_NAME=Jobbify

For the job-api backend, create a .env file in the job-api directory with:

# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_service_key

# AI APIs
OPENROUTER_API_KEY=your_openrouter_key_here
OPENAI_API_KEY=your_openai_api_key

# Job APIs
ADZUNA_APP_ID=your_adzuna_app_id
ADZUNA_APP_KEY=your_adzuna_app_key
JOOBLE_API_KEY=your_jooble_api_key

# Redis (optional)
REDIS_URL=redis://localhost:6379

For the file_text_backend, create a .env file in the file_text_backend directory with:

# AI APIs
OPENROUTER_API_KEY=your_openrouter_key_here

================================================================================
                            SECURITY NOTES
================================================================================

1. Never commit API keys to version control
2. Use environment variables for all sensitive data
3. Rotate keys regularly
4. Use different keys for development and production
5. Monitor API usage and costs
6. Keep this file secure and local only
7. Consider using a secrets management service for production

================================================================================
                            COST MONITORING
================================================================================

Monitor usage for:
- OpenRouter API calls (pay-per-use)
- Supabase database operations (storage, auth, API calls)
- RapidAPI job fetching (subscription-based)
- File processing API calls (PDF.co)
- Adzuna API requests (1000 daily limit)
- Jooble API requests (1000 daily limit)
- PDF.co processing (document processing)
- Redis cache operations (if using hosted Redis)
- Gemini backend analysis calls (local/cloud)
- The Muse API requests (rate limited)
- Arbeitnow API requests (free, no auth)
- Google OAuth API calls (free tier limits)
- Slack webhook notifications (free)
- OpenAI API requests (if used directly, pay-per-token)
- Ashby API requests (free for job board access)
- RemoteOK API requests (free)
- DeepSeek API requests (via OpenRouter)
- Prometheus metrics collection (free, self-hosted)
- Health check endpoints (free, built-in)

Set up billing alerts where possible to avoid unexpected charges.

================================================================================
                        SECURITY & HARDCODED KEYS
================================================================================

⚠️ SECURITY WARNING: The following hardcoded API keys were found in the codebase:

1. RapidAPI Key in JobsService.ts:
   - Key: **************************************************
   - Location: Jobbify/services/JobsService.ts and rapidApiJobsService.ts
   - Risk: Exposed in client-side code
   - Action Required: Move to environment variables

2. Google Client IDs in multiple files:
   - Web: 363643098728-153alopt4kga1c3fffp2cuuos3ethcch.apps.googleusercontent.com
   - iOS: 363643098728-h7ardkqauedqsddnpri3ef026etkf4rs.apps.googleusercontent.com
   - Location: app.config.js, authUtils.ts, google-services.json
   - Risk: Medium (OAuth client IDs are less sensitive but should be configurable)

3. Supabase Keys in test files:
   - Multiple test files contain hardcoded Supabase anon keys
   - Risk: Low for anon keys, but should use environment variables

4. Default API Keys:
   - Jooble: 9908d703-d6b5-4349-a712-6b5f01483150
   - The Muse: 39a8dde5f23991b29f8097f3638271072d9a749c44ee7f08de7f5b7e404b72d2
   - Location: job-api/job_apis.py
   - Risk: Medium (fallback keys, should be replaced with real keys)

================================================================================
                            TROUBLESHOOTING
================================================================================

Common issues:
1. API key not working: Check expiration and permissions
2. Rate limiting: Implement proper retry logic
3. CORS issues: Ensure proper domain configuration
4. Authentication errors: Verify key format and headers
5. Hardcoded keys: Move sensitive keys to environment variables
6. Client-side exposure: Never expose secret keys in frontend code

================================================================================
                            NEW INTEGRATIONS
================================================================================

Recently documented services (comprehensive API audit):
1. Adzuna API - Job search aggregation with app ID and key
2. PDF.co API - Alternative PDF text extraction service
3. Gemini Backend - Local AI-powered resume analysis
4. Redis Cache - Backend caching and session management
5. The Muse API - Tech and business job listings platform
6. Arbeitnow API - European job market integration (no auth required)
7. Google Services - OAuth authentication for web and iOS
8. Slack Webhooks - Monitoring and alert notifications
9. OpenAI API (Direct) - Alternative to OpenRouter for AI features
10. Prometheus Metrics - Application monitoring and performance tracking
11. Health Check Endpoints - Service monitoring and readiness probes
12. API Endpoints Configuration - Multi-environment URL management
13. File Text Backend - Local PDF and document processing service
14. Enhanced environment variable structure with EXPO_PUBLIC_ prefixes
15. Site configuration variables for OpenRouter integration
16. Security audit of hardcoded API keys and client IDs
17. Multiple Supabase service configurations for different environments
18. Comprehensive cost monitoring for all integrated services
19. Complete environment setup documentation for all components

Last updated: January 2025
Maintained by: Development Team