import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { supabase } from '../../lib/supabase';
import { useAppContext } from '../../context/AppContext';

interface TodoItem {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  completed: boolean;
  priority?: 'low' | 'medium' | 'high';
  due_date?: string;
  created_at: string;
  updated_at: string;
}

interface JobStats {
  applied: number;
  pending: number;
  interviews: number;
}

const { width } = Dimensions.get('window');

export default function HomeScreen() {
  const theme = useTheme();
  const { user, theme: appTheme } = useAppContext();
  const [jobStats, setJobStats] = useState<JobStats>({ applied: 0, pending: 0, interviews: 0 });
  const [todos, setTodos] = useState<TodoItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const isDark = appTheme === 'dark';

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      await Promise.all([loadJobStats(), loadTodos()]);
    } catch (error) {
      console.error('Error loading home data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadData(true);
  };

  const loadJobStats = async () => {
    try {
      const { data: applications, error } = await supabase
        .from('job_applications')
        .select('status')
        .eq('user_id', user?.id);

      if (error) throw error;

      const stats = {
        applied: applications?.length || 0,
        pending: applications?.filter(app => app.status === 'pending').length || 0,
        interviews: applications?.filter(app => app.status === 'interview').length || 0,
      };

      setJobStats(stats);
    } catch (error) {
      console.error('Error loading job stats:', error);
    }
  };

  const loadTodos = async () => {
    try {
      const { data: todos, error } = await supabase
        .from('user_todos')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) {
        throw error;
      }

      setTodos(todos || []);

      // If no todos exist, create some default ones
      if (!todos || todos.length === 0) {
        await createDefaultTodos();
      }
    } catch (error) {
      console.error('Error loading todos:', error);
      setTodos([]);
    }
  };

  const toggleTodo = async (todoId: string) => {
    try {
      const todo = todos.find(t => t.id === todoId);
      if (!todo) return;

      const { error } = await supabase
        .from('user_todos')
        .update({ completed: !todo.completed })
        .eq('id', todoId)
        .eq('user_id', user?.id);

      if (error) throw error;

      setTodos(prev => prev.map(t =>
        t.id === todoId ? { ...t, completed: !t.completed } : t
      ));
    } catch (error) {
      console.error('Error updating todo:', error);
      // Update locally even if database update fails
      setTodos(prev => prev.map(t =>
        t.id === todoId ? { ...t, completed: !t.completed } : t
      ));
    }
  };

  const createDefaultTodos = async () => {
    if (!user?.id) return;

    try {
      const defaultTodos = [
        {
          user_id: user.id,
          title: 'Update your resume',
          description: 'Make sure your resume is up to date with your latest experience',
          priority: 'high' as const,
        },
        {
          user_id: user.id,
          title: 'Apply to 3 new jobs',
          description: 'Set a goal to apply to at least 3 jobs this week',
          priority: 'medium' as const,
        },
        {
          user_id: user.id,
          title: 'Follow up on pending applications',
          description: 'Check the status of applications you submitted last week',
          priority: 'medium' as const,
        },
      ];

      const { error } = await supabase
        .from('user_todos')
        .insert(defaultTodos);

      if (error) throw error;

      // Reload todos after creating defaults
      loadTodos();
    } catch (error) {
      console.error('Error creating default todos:', error);
    }
  };

  const addTodo = async (title: string, description?: string, priority: 'low' | 'medium' | 'high' = 'medium') => {
    if (!user?.id) return;

    try {
      const { error } = await supabase
        .from('user_todos')
        .insert({
          user_id: user.id,
          title,
          description,
          priority,
        });

      if (error) throw error;

      // Reload todos after adding
      loadTodos();
    } catch (error) {
      console.error('Error adding todo:', error);
    }
  };

  const StatCard = ({ title, value, icon, color }: { title: string; value: number; icon: string; color: string }) => (
    <View style={[styles.statCard, { 
      backgroundColor: isDark ? '#1F2937' : '#FFFFFF',
      borderColor: isDark ? '#374151' : '#E5E7EB',
      shadowColor: isDark ? '#000000' : '#000000',
      shadowOpacity: isDark ? 0.3 : 0.1,
    }]}>
      <LinearGradient
        colors={[color + '20', color + '10']}
        style={styles.statIconGradient}
      >
        <FontAwesome name={icon as any} size={28} color={color} />
      </LinearGradient>
      <Text style={[styles.statValue, { color: isDark ? '#F9FAFB' : '#111827' }]}>{value}</Text>
      <Text style={[styles.statTitle, { color: isDark ? '#9CA3AF' : '#6B7280' }]}>{title}</Text>
    </View>
  );

  const TodoItem = ({ todo }: { todo: TodoItem }) => (
    <TouchableOpacity
      style={[styles.todoItem, { 
        backgroundColor: isDark ? '#1F2937' : '#FFFFFF',
        borderColor: isDark ? '#374151' : '#E5E7EB',
        shadowColor: isDark ? '#000000' : '#000000',
        shadowOpacity: isDark ? 0.2 : 0.05,
      }]}
      onPress={() => toggleTodo(todo.id)}
      activeOpacity={0.7}
    >
      <View style={[styles.todoCheckbox, { 
        borderColor: todo.completed ? '#10B981' : (isDark ? '#4B5563' : '#D1D5DB'),
        backgroundColor: todo.completed ? '#10B981' : 'transparent'
      }]}>
        {todo.completed && (
          <FontAwesome name="check" size={14} color="#FFFFFF" />
        )}
      </View>
      <Text
        style={[
          styles.todoText,
          { color: isDark ? '#F9FAFB' : '#111827' },
          todo.completed && { 
            textDecorationLine: 'line-through',
            color: isDark ? '#6B7280' : '#9CA3AF'
          },
        ]}
      >
        {todo.title}
      </Text>
      <View style={styles.todoArrow}>
        <FontAwesome 
          name="chevron-right" 
          size={12} 
          color={isDark ? '#6B7280' : '#9CA3AF'} 
        />
      </View>
    </TouchableOpacity>
  );

  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning! ☀️';
    if (hour < 17) return 'Good afternoon! 🌤️';
    return 'Good evening! 🌙';
  };

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: isDark ? '#111827' : '#F9FAFB' }]}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          tintColor={isDark ? '#9CA3AF' : '#6B7280'}
          colors={['#3B82F6']}
        />
      }
      showsVerticalScrollIndicator={false}
    >
      <LinearGradient
        colors={isDark ? ['#1F2937', '#111827'] : ['#FFFFFF', '#F9FAFB']}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <Text style={[styles.greeting, { color: isDark ? '#F9FAFB' : '#111827' }]}>
            {getTimeBasedGreeting()}
          </Text>
          <Text style={[styles.subtitle, { color: isDark ? '#9CA3AF' : '#6B7280' }]}>
            {user?.name ? `Welcome back, ${user.name.split(' ')[0]}!` : 'Here\'s your job search overview'}
          </Text>
        </View>
      </LinearGradient>

      <View style={styles.statsContainer}>
        <StatCard
          title="Applied"
          value={jobStats.applied}
          icon="briefcase"
          color="#3B82F6"
        />
        <StatCard
          title="Pending"
          value={jobStats.pending}
          icon="clock-o"
          color="#F59E0B"
        />
        <StatCard
          title="Interviews"
          value={jobStats.interviews}
          icon="calendar"
          color="#10B981"
        />
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionTitleContainer}>
            <FontAwesome 
              name="list-ul" 
              size={22} 
              color={isDark ? '#3B82F6' : '#2563EB'} 
              style={styles.sectionIcon}
            />
            <Text style={[styles.sectionTitle, { color: isDark ? '#F9FAFB' : '#111827' }]}>
              Quick Tasks
            </Text>
          </View>
          <View style={[styles.taskCounter, { 
            backgroundColor: isDark ? '#374151' : '#E5E7EB'
          }]}>
            <Text style={[styles.taskCounterText, { 
              color: isDark ? '#9CA3AF' : '#6B7280' 
            }]}>
              {todos.filter(t => !t.completed).length}/{todos.length}
            </Text>
          </View>
        </View>
        
        <View style={styles.todoList}>
          {todos.map((todo) => (
            <TodoItem key={todo.id} todo={todo} />
          ))}
        </View>
      </View>

      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerGradient: {
    paddingHorizontal: 20,
    paddingTop: 80,
    paddingBottom: 10,
  },
  header: {
    marginBottom: 20,
  },
  greeting: {
    fontSize: 32,
    fontWeight: '700',
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 17,
    lineHeight: 24,
    fontWeight: '400',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 20,
    marginBottom: 32,
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 20,
    borderRadius: 20,
    alignItems: 'center',
    borderWidth: 1,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowRadius: 8,
    elevation: 8,
  },
  statIconGradient: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  statValue: {
    fontSize: 28,
    fontWeight: '800',
    marginBottom: 6,
    letterSpacing: -0.5,
  },
  statTitle: {
    fontSize: 13,
    textAlign: 'center',
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIcon: {
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  taskCounter: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  taskCounterText: {
    fontSize: 13,
    fontWeight: '600',
  },
  todoList: {
    gap: 12,
  },
  todoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 18,
    borderRadius: 16,
    borderWidth: 1,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowRadius: 4,
    elevation: 3,
  },
  todoCheckbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  todoText: {
    flex: 1,
    fontSize: 16,
    lineHeight: 22,
    fontWeight: '500',
  },
  todoArrow: {
    marginLeft: 12,
    opacity: 0.6,
  },
  bottomSpacing: {
    height: 100,
  },
});