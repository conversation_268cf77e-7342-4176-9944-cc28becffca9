export default {
  expo: {
    name: "Jobbify",
    slug: "jobbify",
    version: "1.0.0",
    scheme: "jobbify",
    sdkVersion: "53.0.0",
    extra: {
      SUPABASE_URL: process.env.EXPO_PUBLIC_SUPABASE_URL || "https://ubueawlkwlvgzxcslats.supabase.co",
      SUPABASE_ANON_KEY: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || "",
      GOOGLE_IOS_CLIENT_ID: process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID || "",
      GOOGLE_WEB_CLIENT_ID: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID || "",
      RAPIDAPI_KEY: process.env.EXPO_PUBLIC_RAPIDAPI_KEY || "",
      // Ashby API Configuration - Demo setup using <PERSON><PERSON>'s own job board
      ASHBY_JO<PERSON>_BOARD_NAME: "<PERSON><PERSON>", // Demo: Using <PERSON><PERSON>'s own job board (3 jobs available)
      ASHBY_INCLUDE_COMPENSATION: "true", // Include salary and compensation data
      eas: {
        projectId: "e7de3ae9-1f5f-4cb7-b0d2-2150d51197b2"
      }
    },
    plugins: [
      "expo-secure-store", 
      "expo-router",
      [
        "@react-native-google-signin/google-signin",
        {
          "webClientId": "363643098728-153alopt4kga1c3fffp2cuuos3ethcch.apps.googleusercontent.com",
          "iosUrlScheme": "com.googleusercontent.apps.363643098728-h7ardkqauedqsddnpri3ef026etkf4rs"
        }
      ]
    ],
    ios: {
      bundleIdentifier: "com.jobbify.mobile",
      infoPlist: {
        CFBundleURLTypes: [
          {
            CFBundleURLSchemes: [
              "com.googleusercontent.apps.363643098728-h7ardkqauedqsddnpri3ef026etkf4rs"
            ]
          }
        ]
      }
    },
    android: {
      package: "com.jobbify.mobile",
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#FFFFFF"
      }
    }
  }
}
